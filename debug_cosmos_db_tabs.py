#!/usr/bin/env python3
"""
调试Cosmos DB的tab结构和重复表格问题
"""

from bs4 import BeautifulSoup
import re

def analyze_cosmos_db_tabs():
    """分析Cosmos DB的tab结构"""
    
    with open('prod-html/cosmos-db-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    print("🔍 Cosmos DB Tab结构分析")
    print("=" * 80)
    
    # 查找tab导航
    tab_navs = soup.find_all('a', {'data-href': re.compile(r'#tabContent1-[123]')})
    
    print("发现的Tab导航:")
    for nav in tab_navs:
        tab_id = nav.get('data-href')
        tab_name = nav.get_text(strip=True)
        print(f"  - {tab_id}: {tab_name}")
    
    print("\n" + "="*80)
    
    # 分析每个tab的内容
    tabs = [
        ('tabContent1-1', '标准预配吞吐量'),
        ('tabContent1-2', '自动缩放预配吞吐量'), 
        ('tabContent1-3', '无服务器')
    ]
    
    for tab_id, tab_name in tabs:
        print(f"\n🔍 分析Tab: {tab_name} ({tab_id})")
        
        # 查找tab内容区域
        tab_panel = soup.find('div', {'id': tab_id})
        if not tab_panel:
            print(f"   ❌ 未找到tab内容区域")
            continue
        
        # 查找该tab中的所有表格
        tables = tab_panel.find_all('table', {'id': True})
        
        print(f"   发现 {len(tables)} 个表格:")
        for table in tables:
            table_id = table.get('id')
            
            # 查找表格标题
            title_element = table.find_previous(['h2', 'h3', 'h4'])
            title = title_element.get_text(strip=True) if title_element else "无标题"
            
            # 分析表格内容
            tbody = table.find('tbody')
            if not tbody:
                rows = table.find_all('tr')[1:]  # 跳过表头
            else:
                rows = tbody.find_all('tr')[1:]  # 跳过表头
            
            valid_rows = 0
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    # 跳过表头行
                    if any(cell.find('strong') for cell in cells):
                        continue
                    
                    name = cells[0].get_text(strip=True)
                    if name and name not in ['实例', '']:
                        valid_rows += 1
            
            print(f"     - {table_id}: {title} ({valid_rows}行数据)")

def check_cosmos_db_duplicates():
    """检查Cosmos DB的重复表格"""
    
    with open('prod-html/cosmos-db-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    print("\n🔍 Cosmos DB重复表格分析")
    print("=" * 80)
    
    # 查找所有cosmos相关的表格
    pattern = r"cosmos-db|cosmos-\d+"
    tables = soup.find_all('table', {'id': True})
    
    cosmos_tables = []
    for table in tables:
        table_id = table.get('id')
        if re.search(pattern, table_id):
            cosmos_tables.append((table_id, table))
    
    print(f"发现 {len(cosmos_tables)} 个Cosmos DB表格:")
    
    # 按类型分组分析
    storage_tables = []
    backup_tables = []
    throughput_tables = []
    transaction_tables = []
    
    for table_id, table in cosmos_tables:
        # 查找表格标题
        title_element = table.find_previous(['h2', 'h3', 'h4'])
        title = title_element.get_text(strip=True) if title_element else ""
        
        print(f"  - {table_id}: {title}")
        
        # 分类
        if "存储" in title:
            storage_tables.append((table_id, title))
        elif "备份" in title:
            backup_tables.append((table_id, title))
        elif "吞吐量" in title or "RU/s" in title:
            throughput_tables.append((table_id, title))
        elif "事务" in title:
            transaction_tables.append((table_id, title))
    
    print(f"\n📊 表格分类统计:")
    print(f"  - 存储表格: {len(storage_tables)}个")
    for table_id, title in storage_tables:
        print(f"    * {table_id}: {title}")
    
    print(f"  - 备份表格: {len(backup_tables)}个")
    for table_id, title in backup_tables:
        print(f"    * {table_id}: {title}")
    
    print(f"  - 吞吐量表格: {len(throughput_tables)}个")
    for table_id, title in throughput_tables:
        print(f"    * {table_id}: {title}")
    
    print(f"  - 事务表格: {len(transaction_tables)}个")
    for table_id, title in transaction_tables:
        print(f"    * {table_id}: {title}")

def analyze_table_content_similarity():
    """分析表格内容相似性"""
    
    from cosmos_db_extractor import CosmosDBExtractor
    
    extractor = CosmosDBExtractor()
    
    with open('prod-html/cosmos-db-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    print("\n🔍 表格内容相似性分析")
    print("=" * 80)
    
    # 获取所有表格的内容签名
    pattern = r"cosmos-db|cosmos-\d+"
    tables = soup.find_all('table', {'id': True})
    
    table_signatures = {}
    
    for table in tables:
        table_id = table.get('id')
        if re.search(pattern, table_id):
            full_table_id = f"#{table_id}"
            pricing_table = extractor.extract_single_pricing_table(soup, full_table_id)
            
            if pricing_table and pricing_table.instances:
                # 生成内容签名
                signature = extractor._generate_table_signature(pricing_table)
                
                if signature in table_signatures:
                    print(f"⚠️ 发现重复内容:")
                    print(f"   原表格: {table_signatures[signature]}")
                    print(f"   重复表格: {full_table_id}")
                    print(f"   签名: {signature[:100]}...")
                    print()
                else:
                    table_signatures[signature] = full_table_id
                    print(f"✓ 唯一表格: {full_table_id}")

if __name__ == "__main__":
    # 1. 分析tab结构
    analyze_cosmos_db_tabs()
    
    # 2. 检查重复表格
    check_cosmos_db_duplicates()
    
    # 3. 分析内容相似性
    analyze_table_content_similarity()
