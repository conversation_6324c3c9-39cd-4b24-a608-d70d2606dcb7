# MySQL HTML解析器 - 区域过滤功能指南

## 概述

MySQL HTML解析器现已支持基于区域的表格过滤功能，可以根据配置文件中的规则自动过滤不同区域不适用的定价表格。

## 功能特性

### 区域过滤规则

解析器实现了以下三种过滤规则：

1. **区域不在配置中** → 显示所有表格
2. **区域在配置中但tableIDs为空数组** → 显示所有表格  
3. **区域在配置中且tableIDs有内容** → 排除指定的表格

### 配置文件格式

配置文件 `soft-category.json` 的格式如下：

```json
[
  {
    "os": "Azure Database for MySQL",
    "region": "east-china3",
    "tableIDs": [
      "#Azure_Database_For_MySQL6",
      "#Azure_Database_For_MySQL4",
      "#Azure_Database_For_MySQL19"
    ]
  }
]
```

## 使用方法

### 基本用法

```python
from mysql_html_parser import MySQLPricingParser

# 读取HTML文件
with open('prod-html/mysql-index.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

# 创建解析器（自动加载 soft-category.json）
parser = MySQLPricingParser(html_content)

# 解析并应用区域过滤
results = parser.parse_all()
```

### 自定义配置文件

```python
# 使用自定义配置文件
parser = MySQLPricingParser(html_content, config_file_path="custom-config.json")
results = parser.parse_all()
```

## 输出结果

解析结果包含以下新增的区域过滤信息：

```json
{
  "region_filter_info": {
    "active_region": "east-china3",
    "filter_config": {
      "east-china3": ["#Azure_Database_For_MySQL6", "..."]
    },
    "filtered_tables": [
      {
        "table_id": "Azure_Database_For_MySQL6",
        "reason": "excluded by region east-china3",
        "filter_config": ["..."]
      }
    ],
    "total_filtered": 7
  }
}
```

## 测试示例

运行测试脚本查看过滤效果：

```bash
python test_region_filtering.py
```

### 测试输出示例

```
=== 测试区域过滤功能 ===

1. 当前激活区域过滤测试:
   激活区域: east-china3
   总表格数: 11
   过滤表格数: 7
   保留表格数: 4

2. 配置文件规则验证:
   east-china3: 排除 23 个表格
     -> 当前激活区域，实际过滤了 7 个表格

4. 保留的表格:
   - Azure_Database_For_MySQL5: 可突发计算 (compute)
   - Azure_Database_For_MySQL_IOPS_East3: 付费 IO (iops)
   - Azure_Database_For_MySQL7: D 系列 (compute)
   - Azure_Database_For_MySQL8: E 系列 (compute)
```

## 技术实现

### 核心方法

1. **`_load_region_filter_config()`** - 加载配置文件
2. **`_should_filter_table(table_id)`** - 判断表格是否应被过滤
3. **`_parse_regions()`** - 识别当前激活区域
4. **`_parse_all_pricing_tables()`** - 应用过滤逻辑

### 过滤逻辑

```python
def _should_filter_table(self, table_id: str) -> bool:
    if not self.active_region:
        return False
        
    # 规则1: 区域不在配置中
    if self.active_region not in self.region_filter_config:
        return False
        
    # 规则2: tableIDs为空
    excluded_tables = self.region_filter_config[self.active_region]
    if not excluded_tables:
        return False
        
    # 规则3: 检查是否在排除列表中
    return table_id in excluded_tables or f"#{table_id}" in excluded_tables
```

## 版本信息

- **解析器版本**: 1.1
- **新增功能**: 区域过滤
- **配置文件**: soft-category.json
- **兼容性**: 向后兼容，不影响现有功能

## 注意事项

1. 配置文件必须是有效的JSON格式
2. 表格ID支持带#和不带#两种格式
3. 如果配置文件不存在，解析器会显示警告但继续工作
4. 过滤信息会记录在解析结果中，便于调试和审计

## 故障排除

### 常见问题

1. **配置文件加载失败**
   - 检查文件路径是否正确
   - 验证JSON格式是否有效

2. **过滤效果不符合预期**
   - 检查区域ID是否匹配
   - 验证表格ID格式（是否包含#）

3. **没有检测到激活区域**
   - 检查HTML中的区域下拉框结构
   - 确认active类或selected属性设置正确
