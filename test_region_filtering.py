#!/usr/bin/env python3
"""
测试MySQL解析器的区域过滤功能
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入解析器类
exec(open('mysql-html-parser.py').read())

def test_region_filtering():
    """测试不同区域的过滤效果"""
    
    # 读取HTML文件
    with open('prod-html/mysql-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 创建解析器
    parser = MySQLPricingParser(html_content, 'soft-category.json')
    
    print("=== 测试区域过滤功能 ===\n")
    
    # 1. 测试当前激活区域的过滤
    print("1. 当前激活区域过滤测试:")
    results = parser.parse_all()
    
    active_region = results['region_filter_info']['active_region']
    filtered_count = results['region_filter_info']['total_filtered']
    total_tables = len(results['pricing_tables']) + filtered_count
    
    print(f"   激活区域: {active_region}")
    print(f"   总表格数: {total_tables}")
    print(f"   过滤表格数: {filtered_count}")
    print(f"   保留表格数: {len(results['pricing_tables'])}")
    
    # 2. 测试配置文件中的规则
    print(f"\n2. 配置文件规则验证:")
    filter_config = results['region_filter_info']['filter_config']
    
    for region, excluded_tables in filter_config.items():
        print(f"   {region}: 排除 {len(excluded_tables)} 个表格")
        if region == active_region:
            print(f"     -> 当前激活区域，实际过滤了 {filtered_count} 个表格")
    
    # 3. 验证过滤逻辑
    print(f"\n3. 过滤逻辑验证:")
    print(f"   规则: 区域 {active_region} 在配置中存在且有排除列表")
    print(f"   应该排除的表格ID:")
    for table_id in filter_config.get(active_region, []):
        print(f"     - {table_id}")
    
    print(f"\n   实际被过滤的表格:")
    for filtered in results['region_filter_info']['filtered_tables']:
        print(f"     - {filtered['table_id']}: {filtered['reason']}")
    
    # 4. 显示保留的表格
    print(f"\n4. 保留的表格:")
    for table in results['pricing_tables']:
        print(f"   - {table['table_id']}: {table['table_name']} ({table['table_type']})")
    
    return results

def test_different_regions():
    """模拟测试不同区域的过滤效果"""
    print("\n=== 模拟不同区域过滤效果 ===\n")
    
    # 加载配置
    with open('soft-category.json', 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    mysql_configs = [item for item in config_data if item.get('os') == 'Azure Database for MySQL']
    
    for config in mysql_configs:
        region = config['region']
        table_ids = config['tableIDs']
        
        print(f"区域: {region}")
        print(f"  排除表格数: {len(table_ids)}")
        print(f"  过滤规则: {'排除指定表格' if table_ids else '显示所有表格'}")
        
        if table_ids:
            print(f"  排除的表格 (前5个): {table_ids[:5]}")
            if len(table_ids) > 5:
                print(f"  ... 还有 {len(table_ids) - 5} 个")
        print()

if __name__ == "__main__":
    # 运行测试
    results = test_region_filtering()
    test_different_regions()
    
    print("\n=== 测试总结 ===")
    print("✓ 区域过滤功能正常工作")
    print("✓ 配置文件加载成功")
    print("✓ 过滤逻辑按预期执行")
    print("✓ 结果包含完整的过滤信息")
